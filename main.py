import os
from datetime import datetime
from fasthtml.common import *
from openai import OpenAI

# Initialize OpenAI client (handle missing API key gracefully)
api_key = os.getenv("OPENAI_API_KEY")
client = OpenAI(api_key=api_key) if api_key else None

# FastHTML app setup with TailwindCSS
hdrs = (
    Script(src="https://cdn.tailwindcss.com"),
    <PERSON>ript(src="https://unpkg.com/htmx.org@1.9.10"),
)
app, rt = fast_app(hdrs=hdrs, live=True, pico=False)

# In-memory storage for conversation history
conversation_history = []

def message_bubble(content, is_user=True, timestamp=None):
    """Create a message bubble component"""
    if timestamp is None:
        timestamp = datetime.now().strftime("%H:%M")
    
    if is_user:
        return Div(
            Div(
                Div(content, cls="bg-blue-500 text-white rounded-lg px-4 py-2 max-w-xs lg:max-w-md"),
                Div(timestamp, cls="text-xs text-gray-500 mt-1"),
                cls="flex flex-col items-end"
            ),
            cls="flex justify-end mb-4"
        )
    else:
        return Div(
            Div(
                Div(
                    Div(cls="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3", children="AI"),
                    Div(
                        Div(content, cls="bg-gray-100 text-gray-800 rounded-lg px-4 py-2 max-w-xs lg:max-w-md"),
                        Div(timestamp, cls="text-xs text-gray-500 mt-1"),
                        cls="flex flex-col"
                    ),
                    cls="flex items-start"
                ),
                cls="flex"
            ),
            cls="mb-4"
        )

def loading_indicator():
    """Create a loading indicator for AI response"""
    return Div(
        Div(
            Div(
                Div(cls="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3", children="AI"),
                Div(
                    Div(
                        Div(cls="flex space-x-1"),
                        Div(cls="w-2 h-2 bg-gray-400 rounded-full animate-bounce"),
                        Div(cls="w-2 h-2 bg-gray-400 rounded-full animate-bounce", style="animation-delay: 0.1s"),
                        Div(cls="w-2 h-2 bg-gray-400 rounded-full animate-bounce", style="animation-delay: 0.2s"),
                        cls="bg-gray-100 rounded-lg px-4 py-2 flex space-x-1"
                    ),
                    cls="flex flex-col"
                ),
                cls="flex items-start"
            ),
            cls="flex"
        ),
        cls="mb-4",
        id="loading-indicator"
    )

def chat_container():
    """Create the main chat container"""
    return Div(
        # Chat messages area
        Div(
            map(lambda msg: message_bubble(msg["content"], msg["is_user"], msg["timestamp"]), conversation_history),
            id="chat-messages",
            cls="flex-1 overflow-y-auto p-4 space-y-4"
        ),
        
        # Input form
        Form(
            Div(
                Input(
                    type="text",
                    name="message",
                    placeholder="Type your message here...",
                    cls="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",
                    required=True,
                    autocomplete="off"
                ),
                Button(
                    "Send",
                    type="submit",
                    cls="ml-2 bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
                ),
                cls="flex"
            ),
            hx_post="/send_message",
            hx_target="#chat-messages",
            hx_swap="innerHTML",
            hx_on__after_request="this.reset(); document.getElementById('chat-messages').scrollTop = document.getElementById('chat-messages').scrollHeight",
            cls="p-4 border-t border-gray-200"
        ),
        cls="flex flex-col h-screen max-w-4xl mx-auto bg-white"
    )

@rt
def index():
    """Main chat interface"""
    return Html(
        Head(
            Title("ChatGPT Clone"),
            Meta(charset="utf-8"),
            Meta(name="viewport", content="width=device-width, initial-scale=1"),
            *hdrs
        ),
        Body(
            Div(
                # Header
                Div(
                    H1("ChatGPT Clone", cls="text-2xl font-bold text-gray-800"),
                    P("Powered by FastHTML and OpenAI", cls="text-gray-600"),
                    cls="text-center py-6 border-b border-gray-200"
                ),
                
                # Chat container
                chat_container(),
                cls="min-h-screen bg-gray-50"
            ),
            cls="font-sans"
        )
    )

@rt
def send_message(message: str):
    """Handle sending a message and getting AI response"""
    if not message.strip():
        return map(lambda msg: message_bubble(msg["content"], msg["is_user"], msg["timestamp"]), conversation_history)
    
    # Add user message to history
    timestamp = datetime.now().strftime("%H:%M")
    conversation_history.append({
        "content": message,
        "is_user": True,
        "timestamp": timestamp
    })
    
    # Show loading indicator
    messages_with_loading = list(conversation_history) + [{"content": "", "is_user": False, "timestamp": "", "loading": True}]
    
    try:
        if not client:
            # Handle missing API key
            ai_response = "Sorry, OpenAI API key is not configured. Please set the OPENAI_API_KEY environment variable to use this chat application."
        else:
            # Prepare conversation for OpenAI API
            openai_messages = []
            for msg in conversation_history:
                role = "user" if msg["is_user"] else "assistant"
                openai_messages.append({"role": role, "content": msg["content"]})
            
            # Get AI response
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=openai_messages,
                max_tokens=1000,
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
        
        # Add AI response to history
        ai_timestamp = datetime.now().strftime("%H:%M")
        conversation_history.append({
            "content": ai_response,
            "is_user": False,
            "timestamp": ai_timestamp
        })
        
    except Exception as e:
        # Handle API errors
        error_message = f"Sorry, I encountered an error: {str(e)}"
        ai_timestamp = datetime.now().strftime("%H:%M")
        conversation_history.append({
            "content": error_message,
            "is_user": False,
            "timestamp": ai_timestamp
        })
    
    # Return updated chat messages
    return map(lambda msg: message_bubble(msg["content"], msg["is_user"], msg["timestamp"]), conversation_history)

@rt
def clear_chat():
    """Clear the conversation history"""
    global conversation_history
    conversation_history = []
    return ""

serve()